# Svelte 5 Authentication Implementation

## Overview

This document outlines the complete authentication system implemented for the Svelte 5 frontend, using reactive class patterns with context, Zod validation, Superforms, and cookie-based JWT storage.

## Architecture

### Core Components

1. **Authentication Store** (`/lib/stores/auth.svelte.ts`)
   - Reactive class using Svelte 5 runes (`$state`, `$derived`)
   - Context pattern for sharing across components
   - JWT token management with secure cookies
   - User state management and permission checking

2. **Authentication Service** (`/lib/services/auth.ts`)
   - API communication with Django backend
   - Token refresh logic
   - Error handling and response formatting
   - JWT token decoding utilities

3. **Cookie Utilities** (`/lib/utils/cookies.ts`)
   - Secure cookie management for JWT tokens
   - HttpOnly cookie handling for security
   - Token expiration management

4. **Validation Schemas** (`/lib/schemas/auth.ts`)
   - Zod schemas for form validation
   - Type-safe form data handling
   - Client-side validation rules

5. **Authentication Hooks** (`/lib/hooks/auth.ts`)
   - Route protection utilities
   - Permission and role guards
   - Convenient auth state access

## Implementation Details

### Authentication Flow

1. **Login Process**:
   - User submits credentials via Superforms
   - Form validation using Zod schemas
   - API call to Django backend (`/auth/login/`)
   - JWT tokens stored in secure cookies
   - User data decoded from access token
   - Reactive state updated automatically

2. **Token Management**:
   - Access tokens stored with 5-minute expiration
   - Refresh tokens stored with 7-day expiration
   - Automatic token refresh on expiration
   - Secure cookie configuration (HttpOnly, Secure, SameSite)

3. **State Persistence**:
   - Authentication state initialized from cookies on app load
   - SSR-safe implementation with browser guards
   - Automatic cleanup on logout

### File Structure

```
src/
├── lib/
│   ├── stores/
│   │   └── auth.svelte.ts          # Main auth store with reactive class
│   ├── services/
│   │   └── auth.ts                 # API service for backend communication
│   ├── utils/
│   │   └── cookies.ts              # Cookie management utilities
│   ├── schemas/
│   │   └── auth.ts                 # Zod validation schemas
│   └── hooks/
│       └── auth.ts                 # Authentication hooks and guards
├── routes/
│   ├── +layout.svelte              # Root layout with auth context
│   ├── (public)/
│   │   └── (auth)/
│   │       └── login/
│   │           └── +page.svelte    # Login page with Superforms
│   └── (protected)/
│       └── dashboard/
│           └── +page.svelte        # Protected dashboard example
└── types/
    └── auth.ts                     # TypeScript interfaces
```

## Usage Examples

### Setting Up Authentication Context

```typescript
// In +layout.svelte
import { setAuthContext } from "$lib/stores/auth.svelte";

const authStore = setAuthContext();
```

### Using Authentication in Components

```typescript
// In any component
import { getAuthContext } from "$lib/stores/auth.svelte";

const authStore = getAuthContext();

// Access reactive state
$: isLoggedIn = authStore.isAuthenticated;
$: currentUser = authStore.user;
```

### Using Authentication Hooks

```typescript
// For convenient access
import { useAuth, useAuthGuard } from "$lib/hooks/auth";

const auth = useAuth();
const isAuthenticated = useAuthGuard(); // Redirects if not authenticated
```

### Form Integration with Superforms

```typescript
import { superForm } from "sveltekit-superforms";
import { zod } from "sveltekit-superforms/adapters";
import { loginSchema } from "$lib/schemas/auth";

const { form, errors, enhance } = superForm(
  { email: '', password: '' },
  {
    validators: zod(loginSchema),
    onSubmit: async ({ formData }) => {
      const success = await authStore.login({
        email: formData.get('email'),
        password: formData.get('password')
      });
    }
  }
);
```

## Security Features

1. **Cookie Security**:
   - HttpOnly cookies prevent XSS attacks
   - Secure flag ensures HTTPS-only transmission
   - SameSite=strict prevents CSRF attacks

2. **Token Management**:
   - Short-lived access tokens (5 minutes)
   - Automatic token refresh
   - Secure token storage

3. **Type Safety**:
   - Full TypeScript coverage
   - Zod validation for runtime safety
   - Strict type checking

## API Integration

### Backend Endpoints

- `POST /auth/login/` - User authentication
- `POST /auth/token/refresh/` - Token refresh

### Expected Response Format

```json
// Login response
{
  "access": "jwt_access_token",
  "refresh": "jwt_refresh_token"
}

// Refresh response
{
  "access": "new_jwt_access_token"
}
```

## Testing the Implementation

1. **Start the development server**:
   ```bash
   cd client
   npm run dev
   ```

2. **Test login flow**:
   - Navigate to `/login`
   - Enter valid credentials
   - Should redirect to `/dashboard` on success
   - Check browser cookies for JWT tokens

3. **Test authentication persistence**:
   - Refresh the page
   - Authentication state should persist
   - User should remain logged in

4. **Test logout**:
   - Click logout button
   - Should clear cookies and redirect to login

## Next Steps

1. **Add registration functionality**
2. **Implement password reset flow**
3. **Add role-based route protection**
4. **Implement user profile management**
5. **Add comprehensive error handling**
6. **Write unit tests for auth components**

## Dependencies

- `zod` - Schema validation
- `sveltekit-superforms` - Form handling
- `svelte` - Framework with runes support

## Notes

- No localStorage/sessionStorage used (cookie-only storage)
- SSR-compatible implementation
- Follows Svelte 5 best practices with runes
- Production-ready security configuration

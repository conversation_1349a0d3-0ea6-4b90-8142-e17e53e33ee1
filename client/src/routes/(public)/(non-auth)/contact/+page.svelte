<script lang="ts">
	/**
	 * Contact Page Component
	 *
	 * Comprehensive contact page featuring:
	 * - Multiple contact methods and channels
	 * - Interactive contact form for inquiries
	 * - Office locations and contact information
	 * - Support resources and FAQ section
	 * - Sales and demo scheduling options
	 *
	 * Design follows established patterns with alternating animated backgrounds
	 * between sections to maintain visual consistency with other pages.
	 */

	import { But<PERSON> } from "$lib/components/ui/button";
	import SectionHeader from "$lib/components/ui/section-header/SectionHeader.svelte";
	import * as Card from "$lib/components/ui/card";
	import { Badge } from "$lib/components/ui/badge";
	import { Input } from "$lib/components/ui/input";
	import { Textarea } from "$lib/components/ui/textarea";
	import * as Accordion from "$lib/components/ui/accordion";

	// Contact methods and channels
	const contactMethods = [
		{
			title: "Sales Inquiries",
			description: "Ready to transform your institution? Our sales team will help you find the perfect plan and get started.",
			icon: "💼",
			contact: "<EMAIL>",
			action: "Schedule Demo",
			href: "/schedule-demo",
			category: "Sales"
		},
		{
			title: "Technical Support",
			description: "Need help with your existing Evoprof implementation? Our technical team is here to assist you.",
			icon: "🛠️",
			contact: "<EMAIL>",
			action: "Get Support",
			href: "mailto:<EMAIL>",
			category: "Support"
		},
		{
			title: "Partnership Opportunities",
			description: "Interested in partnering with Evoprof? Let's explore how we can work together to improve education.",
			icon: "🤝",
			contact: "<EMAIL>",
			action: "Discuss Partnership",
			href: "mailto:<EMAIL>",
			category: "Partnership"
		},
		{
			title: "General Inquiries",
			description: "Have questions about Evoprof or need more information? We're here to help with any questions.",
			icon: "💬",
			contact: "<EMAIL>",
			action: "Send Message",
			href: "mailto:<EMAIL>",
			category: "General"
		},
		{
			title: "Media & Press",
			description: "Media inquiries, press releases, and interview requests. Connect with our communications team.",
			icon: "📰",
			contact: "<EMAIL>",
			action: "Media Inquiry",
			href: "mailto:<EMAIL>",
			category: "Media"
		},
		{
			title: "Career Opportunities",
			description: "Join our mission to transform education in Kenya. Explore career opportunities with the Evoprof team.",
			icon: "🚀",
			contact: "<EMAIL>",
			action: "View Careers",
			href: "mailto:<EMAIL>",
			category: "Careers"
		}
	];

	// Office locations and contact information
	const officeLocations = [
		{
			city: "Nairobi",
			country: "Kenya",
			address: "Westlands Business District\nWaiyaki Way, Nairobi\nKenya",
			phone: "+*********** 456",
			email: "<EMAIL>",
			hours: "Monday - Friday: 8:00 AM - 6:00 PM EAT",
			icon: "🏢"
		},
		{
			city: "Mombasa",
			country: "Kenya",
			address: "Nyali Business Center\nLinks Road, Mombasa\nKenya",
			phone: "+*********** 457",
			email: "<EMAIL>",
			hours: "Monday - Friday: 8:00 AM - 6:00 PM EAT",
			icon: "🌊"
		},
		{
			city: "Kisumu",
			country: "Kenya",
			address: "Milimani Commercial Complex\nOginga Odinga Street, Kisumu\nKenya",
			phone: "+*********** 458",
			email: "<EMAIL>",
			hours: "Monday - Friday: 8:00 AM - 6:00 PM EAT",
			icon: "🏛️"
		}
	];

	// Contact form state
	let formData = $state({
		name: "",
		email: "",
		organization: "",
		phone: "",
		inquiryType: "general",
		message: ""
	});

	// Form submission handler
	function handleSubmit(event: Event) {
		event.preventDefault();
		// TODO: Implement form submission logic
		console.log("Form submitted:", formData);
		// Reset form or show success message
	}

	// Contact FAQ
	const contactFAQs = [
		{
			question: "How quickly can I expect a response to my inquiry?",
			answer: "We typically respond to all inquiries within 24 hours during business days. For urgent technical support issues, we aim to respond within 4 hours."
		},
		{
			question: "Can I schedule a demo of Evoprof?",
			answer: "Absolutely! You can schedule a personalized demo by clicking the 'Schedule Demo' button or contacting our sales team directly. Demos typically last 30-45 minutes and can be customized to your institution's needs."
		},
		{
			question: "Do you offer on-site training and support?",
			answer: "Yes, we provide comprehensive on-site training and support for institutional clients. Our team can visit your location to provide hands-on training for administrators, teachers, and staff."
		},
		{
			question: "What languages do you provide support in?",
			answer: "We provide support in English and Swahili. Our team is fluent in both languages and can assist you in whichever language you're most comfortable with."
		},
		{
			question: "How do I report a technical issue or bug?",
			answer: "Technical issues can be reported through our support email (<EMAIL>), through the in-app support system, or by calling our technical support line. Please include as much detail as possible about the issue."
		}
	];
</script>

<div class="page-spacing">
	<!-- Hero Section with Animated Background -->
	<section class="section-spacing">
		<SectionHeader
			sectionName="Contact Us"
			headline="Get in touch with our team"
			subheadline="<p>Whether you're ready to get started, need support, or have questions about Evoprof, we're here to help. Reach out through any of the channels below.</p>"
			color="#3B82F6"
		/>
	</section>

	<!-- Contact Methods Section (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="text-center space-y-4">
			<div class="subsection-subitem-text py-0.1 flex h-fit w-fit items-center justify-center rounded-full border px-2 font-[400] tracking-tighter mx-auto"
				 style="color: #10B981; background-color: color-mix(in srgb, #10B981 20%, transparent);">
				Contact Methods
			</div>
			<h2 class="section-headline-text">Choose how you'd like to connect</h2>
			<p class="section-subheadline-text">Multiple ways to reach our team based on your specific needs and preferences.</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each contactMethods as method (method.title)}
				<Card.Root class="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
					<Card.Content class="p-6">
						<div class="flex flex-col items-start gap-4">
							<div class="flex items-center gap-3">
								<div class="text-3xl">{method.icon}</div>
								<Badge variant="secondary" class="bg-emerald-100 text-emerald-700 dark:bg-emerald-900 dark:text-emerald-300">
									{method.category}
								</Badge>
							</div>
							<div class="space-y-3">
								<Card.Title class="text-lg font-semibold group-hover:text-primary transition-colors">
									{method.title}
								</Card.Title>
								<Card.Description class="text-muted-foreground leading-relaxed">
									{method.description}
								</Card.Description>
								<div class="space-y-2">
									<p class="text-sm font-medium text-primary">{method.contact}</p>
									<Button size="sm" href={method.href} class="w-full">
										{method.action}
									</Button>
								</div>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- Contact Form Section with Animated Background -->
	<section class="section-spacing">
		<SectionHeader
			sectionName="Send us a Message"
			headline="Tell us about your needs"
			subheadline="<p>Fill out the form below and our team will get back to you within 24 hours. We'll help you find the right solution for your institution.</p>"
			color="#8B5CF6"
		/>
	</section>

	<!-- Contact Form (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="w-full max-w-2xl">
			<Card.Root>
				<Card.Content class="p-8">
					<form onsubmit={handleSubmit} class="space-y-6">
						<!-- Name and Email Row -->
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div class="space-y-2">
								<label for="name" class="text-sm font-medium">Full Name *</label>
								<Input
									id="name"
									type="text"
									placeholder="Your full name"
									bind:value={formData.name}
									required
								/>
							</div>
							<div class="space-y-2">
								<label for="email" class="text-sm font-medium">Email Address *</label>
								<Input
									id="email"
									type="email"
									placeholder="<EMAIL>"
									bind:value={formData.email}
									required
								/>
							</div>
						</div>

						<!-- Organization and Phone Row -->
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div class="space-y-2">
								<label for="organization" class="text-sm font-medium">Organization</label>
								<Input
									id="organization"
									type="text"
									placeholder="Your institution or company"
									bind:value={formData.organization}
								/>
							</div>
							<div class="space-y-2">
								<label for="phone" class="text-sm font-medium">Phone Number</label>
								<Input
									id="phone"
									type="tel"
									placeholder="+*********** 456"
									bind:value={formData.phone}
								/>
							</div>
						</div>

						<!-- Inquiry Type -->
						<div class="space-y-2">
							<label for="inquiryType" class="text-sm font-medium">Inquiry Type *</label>
							<select
								id="inquiryType"
								bind:value={formData.inquiryType}
								class="flex h-9 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
								required
							>
								<option value="general">General Inquiry</option>
								<option value="sales">Sales & Demo</option>
								<option value="support">Technical Support</option>
								<option value="partnership">Partnership</option>
								<option value="media">Media & Press</option>
								<option value="careers">Careers</option>
							</select>
						</div>

						<!-- Message -->
						<div class="space-y-2">
							<label for="message" class="text-sm font-medium">Message *</label>
							<Textarea
								id="message"
								placeholder="Tell us about your needs, questions, or how we can help you..."
								bind:value={formData.message}
								rows={5}
								required
							/>
						</div>

						<!-- Submit Button -->
						<Button type="submit" size="lg" class="w-full">
							Send Message
						</Button>

						<!-- Privacy Notice -->
						<p class="text-xs text-muted-foreground text-center">
							By submitting this form, you agree to our
							<a href="/privacy-policy" class="text-primary hover:underline">Privacy Policy</a>
							and consent to being contacted by our team.
						</p>
					</form>
				</Card.Content>
			</Card.Root>
		</div>
	</section>

	<!-- Office Locations Section (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="text-center space-y-4">
			<div class="subsection-subitem-text py-0.1 flex h-fit w-fit items-center justify-center rounded-full border px-2 font-[400] tracking-tighter mx-auto"
				 style="color: #F59E0B; background-color: color-mix(in srgb, #F59E0B 20%, transparent);">
				Office Locations
			</div>
			<h2 class="section-headline-text">Visit us in person</h2>
			<p class="section-subheadline-text">Our offices across Kenya are ready to welcome you for in-person meetings and consultations.</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each officeLocations as office (office.city)}
				<Card.Root class="hover:shadow-lg transition-all duration-300">
					<Card.Content class="p-6">
						<div class="space-y-4">
							<div class="flex items-center gap-3">
								<div class="text-3xl">{office.icon}</div>
								<div>
									<Card.Title class="text-lg font-semibold">
										{office.city}
									</Card.Title>
									<p class="text-sm text-muted-foreground">{office.country}</p>
								</div>
							</div>
							<div class="space-y-3 text-sm">
								<div>
									<p class="font-medium text-foreground">Address:</p>
									<p class="text-muted-foreground whitespace-pre-line">{office.address}</p>
								</div>
								<div>
									<p class="font-medium text-foreground">Phone:</p>
									<p class="text-muted-foreground">{office.phone}</p>
								</div>
								<div>
									<p class="font-medium text-foreground">Email:</p>
									<p class="text-muted-foreground">{office.email}</p>
								</div>
								<div>
									<p class="font-medium text-foreground">Hours:</p>
									<p class="text-muted-foreground">{office.hours}</p>
								</div>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- FAQ Section with Animated Background -->
	<section class="section-spacing">
		<SectionHeader
			sectionName="Frequently Asked Questions"
			headline="Quick answers to common questions"
			subheadline="<p>Find answers to the most common questions about contacting us and getting support. Still have questions? Don't hesitate to reach out directly.</p>"
			color="#EF4444"
		/>
	</section>

	<!-- FAQ Content (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<Accordion.Root type="multiple" class="w-full max-w-3xl" variant="noBorderGap">
			{#each contactFAQs as faq (faq.question)}
				<Accordion.Item value={faq.question} class="bg-gray rounded-2xl border">
					<Accordion.Trigger class="cursor-pointer">{faq.question}</Accordion.Trigger>
					<Accordion.Content>
						<p>{faq.answer}</p>
					</Accordion.Content>
				</Accordion.Item>
			{/each}
		</Accordion.Root>
	</section>
</div>

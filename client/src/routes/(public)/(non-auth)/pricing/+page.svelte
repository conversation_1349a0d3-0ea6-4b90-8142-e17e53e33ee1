<script lang="ts">
	/**
	 * Pricing Page Component
	 *
	 * Comprehensive pricing page featuring:
	 * - Multiple pricing tiers for different institution sizes
	 * - Feature comparison across plans
	 * - Enterprise and custom solutions
	 * - Transparent pricing with no hidden fees
	 * - FAQ section addressing common pricing questions
	 *
	 * Design follows established patterns with alternating animated backgrounds
	 * between sections to maintain visual consistency with other pages.
	 */

	import { Button } from "$lib/components/ui/button";
	import SectionHeader from "$lib/components/ui/section-header/SectionHeader.svelte";
	import * as Card from "$lib/components/ui/card";
	import { Badge } from "$lib/components/ui/badge";
	import * as Accordion from "$lib/components/ui/accordion";

	// Pricing plans
	const pricingPlans = [
		{
			name: "Starter",
			description: "Perfect for small schools and pilot programs",
			price: "KES 15,000",
			period: "per month",
			studentLimit: "Up to 500 students",
			popular: false,
			features: [
				"Student skill tracking",
				"Basic analytics dashboard",
				"Progress reporting",
				"Email support",
				"Data export (CSV)",
				"Mobile app access",
				"Basic integrations (2)",
				"Monthly data backup"
			],
			limitations: [
				"Limited to 5 administrators",
				"Basic reporting only",
				"Email support only"
			],
			cta: "Start Free Trial",
			href: "/signup?plan=starter"
		},
		{
			name: "Professional",
			description: "Ideal for medium-sized institutions",
			price: "KES 35,000",
			period: "per month",
			studentLimit: "Up to 2,000 students",
			popular: true,
			features: [
				"Everything in Starter",
				"Advanced analytics & insights",
				"AI-powered recommendations",
				"Custom reporting",
				"Priority email & chat support",
				"Advanced integrations (10)",
				"Bulk student onboarding",
				"Real-time data sync",
				"Custom branding",
				"Weekly data backup"
			],
			limitations: [
				"Limited to 20 administrators",
				"Standard API rate limits"
			],
			cta: "Start Free Trial",
			href: "/signup?plan=professional"
		},
		{
			name: "Enterprise",
			description: "For large institutions and university systems",
			price: "Custom",
			period: "pricing",
			studentLimit: "Unlimited students",
			popular: false,
			features: [
				"Everything in Professional",
				"Unlimited administrators",
				"Dedicated account manager",
				"24/7 phone support",
				"Custom integrations",
				"Advanced security features",
				"Blockchain verification",
				"On-premise deployment option",
				"Custom training & onboarding",
				"SLA guarantees",
				"Daily data backup",
				"White-label options"
			],
			limitations: [],
			cta: "Contact Sales",
			href: "/contact-sales"
		}
	];

	// Feature comparison data
	const featureComparison = [
		{
			category: "Core Features",
			features: [
				{ name: "Student skill tracking", starter: true, professional: true, enterprise: true },
				{ name: "Progress reporting", starter: true, professional: true, enterprise: true },
				{ name: "Mobile app access", starter: true, professional: true, enterprise: true },
				{ name: "Basic analytics", starter: true, professional: true, enterprise: true },
				{ name: "Advanced analytics", starter: false, professional: true, enterprise: true },
				{ name: "AI recommendations", starter: false, professional: true, enterprise: true }
			]
		},
		{
			category: "Integrations",
			features: [
				{ name: "Basic integrations", starter: "2", professional: "10", enterprise: "Unlimited" },
				{ name: "LMS integration", starter: false, professional: true, enterprise: true },
				{ name: "SIS integration", starter: false, professional: true, enterprise: true },
				{ name: "Custom integrations", starter: false, professional: false, enterprise: true },
				{ name: "API access", starter: "Limited", professional: "Standard", enterprise: "Full" }
			]
		},
		{
			category: "Support & Security",
			features: [
				{ name: "Email support", starter: true, professional: true, enterprise: true },
				{ name: "Chat support", starter: false, professional: true, enterprise: true },
				{ name: "Phone support", starter: false, professional: false, enterprise: true },
				{ name: "Dedicated account manager", starter: false, professional: false, enterprise: true },
				{ name: "Data encryption", starter: true, professional: true, enterprise: true },
				{ name: "Blockchain verification", starter: false, professional: false, enterprise: true }
			]
		}
	];

	// Pricing FAQ
	const pricingFAQs = [
		{
			question: "Is there a free trial available?",
			answer: "Yes! We offer a 30-day free trial for both Starter and Professional plans. This gives you full access to all features so you can evaluate Evoprof with your actual data and workflows."
		},
		{
			question: "What happens if I exceed my student limit?",
			answer: "If you approach your student limit, we'll notify you in advance. You can easily upgrade to the next tier or contact us for a custom plan. We never cut off access suddenly - we work with you to find the right solution."
		},
		{
			question: "Can I change plans at any time?",
			answer: "Absolutely! You can upgrade or downgrade your plan at any time. Upgrades take effect immediately, while downgrades take effect at your next billing cycle. We'll help you migrate your data seamlessly."
		},
		{
			question: "Are there any setup fees or hidden costs?",
			answer: "No hidden fees! Our pricing is transparent. The only additional costs might be for custom integrations or extensive on-site training, which we'll discuss upfront."
		},
		{
			question: "Do you offer discounts for multiple years?",
			answer: "Yes! We offer significant discounts for annual payments: 15% off for Professional plans and 20% off for Enterprise plans. Contact our sales team for multi-year discount options."
		},
		{
			question: "What payment methods do you accept?",
			answer: "We accept bank transfers, mobile money (M-Pesa, Airtel Money), and major credit cards. For Enterprise clients, we can also arrange invoicing and purchase orders."
		}
	];

	// Additional services
	const additionalServices = [
		{
			name: "Implementation & Training",
			description: "Comprehensive onboarding and training for your team",
			price: "From KES 50,000",
			features: ["On-site training", "Data migration", "Custom workflows", "Go-live support"]
		},
		{
			name: "Custom Integration Development",
			description: "Bespoke integrations with your existing systems",
			price: "From KES 100,000",
			features: ["Custom API development", "Legacy system integration", "Real-time sync", "Ongoing maintenance"]
		},
		{
			name: "Dedicated Support",
			description: "Premium support with guaranteed response times",
			price: "From KES 25,000/month",
			features: ["Dedicated support agent", "Priority response", "Phone support", "Quarterly reviews"]
		}
	];
</script>

<div class="page-spacing">
	<!-- Hero Section with Animated Background -->
	<section class="section-spacing">
		<SectionHeader
			sectionName="Pricing"
			headline="Simple, transparent pricing for every institution"
			subheadline="<p>Choose the plan that fits your institution's size and needs. All plans include core features with no hidden fees. Start with a free trial and scale as you grow.</p>"
			color="#3B82F6"
		/>
	</section>

	<!-- Pricing Plans Section (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="text-center space-y-4">
			<div class="subsection-subitem-text py-0.1 flex h-fit w-fit items-center justify-center rounded-full border px-2 font-[400] tracking-tighter mx-auto"
				 style="color: #10B981; background-color: color-mix(in srgb, #10B981 20%, transparent);">
				Pricing Plans
			</div>
			<h2 class="section-headline-text">Choose your plan</h2>
			<p class="section-subheadline-text">All plans include a 30-day free trial and can be upgraded or downgraded at any time.</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-3 gap-8 w-full max-w-6xl">
			{#each pricingPlans as plan (plan.name)}
				<Card.Root class="relative {plan.popular ? 'ring-2 ring-primary' : ''} hover:shadow-lg transition-all duration-300">
					{#if plan.popular}
						<div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
							<Badge class="bg-primary text-primary-foreground">Most Popular</Badge>
						</div>
					{/if}
					<Card.Content class="p-8">
						<div class="space-y-6">
							<!-- Plan Header -->
							<div class="text-center space-y-2">
								<h3 class="text-2xl font-bold">{plan.name}</h3>
								<p class="text-muted-foreground">{plan.description}</p>
								<div class="space-y-1">
									<div class="text-3xl font-bold">
										{plan.price}
										{#if plan.period !== "pricing"}
											<span class="text-lg font-normal text-muted-foreground">/{plan.period}</span>
										{/if}
									</div>
									<p class="text-sm text-muted-foreground">{plan.studentLimit}</p>
								</div>
							</div>

							<!-- Features List -->
							<div class="space-y-3">
								<h4 class="font-semibold">Included features:</h4>
								<ul class="space-y-2">
									{#each plan.features as feature}
										<li class="flex items-center gap-2 text-sm">
											<span class="text-green-500">✓</span>
											{feature}
										</li>
									{/each}
								</ul>
								{#if plan.limitations.length > 0}
									<div class="pt-2">
										<h5 class="text-sm font-medium text-muted-foreground">Limitations:</h5>
										<ul class="space-y-1 mt-1">
											{#each plan.limitations as limitation}
												<li class="flex items-center gap-2 text-sm text-muted-foreground">
													<span>•</span>
													{limitation}
												</li>
											{/each}
										</ul>
									</div>
								{/if}
							</div>

							<!-- CTA Button -->
							<Button
								href={plan.href}
								class="w-full"
								variant={plan.popular ? "default" : "outline"}
								size="lg"
							>
								{plan.cta}
							</Button>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>

		<!-- Pricing Notes -->
		<div class="text-center space-y-2 max-w-2xl">
			<p class="text-sm text-muted-foreground">
				All prices are in Kenyan Shillings (KES) and exclude applicable taxes.
			</p>
			<p class="text-sm text-muted-foreground">
				Need a custom solution? <a href="/contact-sales" class="text-primary hover:underline">Contact our sales team</a> for enterprise pricing and custom features.
			</p>
		</div>
	</section>

	<!-- Feature Comparison Section with Animated Background -->
	<section class="section-spacing">
		<SectionHeader
			sectionName="Feature Comparison"
			headline="Compare plans side by side"
			subheadline="<p>See exactly what's included in each plan to make the best decision for your institution. All plans can be upgraded at any time as your needs grow.</p>"
			color="#8B5CF6"
		/>
	</section>

	<!-- Feature Comparison Table (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="w-full max-w-5xl overflow-x-auto">
			<div class="min-w-[600px]">
				{#each featureComparison as category (category.category)}
					<div class="mb-8">
						<h3 class="text-lg font-semibold mb-4 text-primary">{category.category}</h3>
						<div class="bg-card rounded-lg border overflow-hidden">
							<!-- Header -->
							<div class="grid grid-cols-4 gap-4 p-4 bg-muted/50 border-b">
								<div class="font-medium">Feature</div>
								<div class="text-center font-medium">Starter</div>
								<div class="text-center font-medium">Professional</div>
								<div class="text-center font-medium">Enterprise</div>
							</div>
							<!-- Features -->
							{#each category.features as feature (feature.name)}
								<div class="grid grid-cols-4 gap-4 p-4 border-b last:border-b-0 hover:bg-muted/30 transition-colors">
									<div class="text-sm">{feature.name}</div>
									<div class="text-center text-sm">
										{#if typeof feature.starter === 'boolean'}
											{#if feature.starter}
												<span class="text-green-500">✓</span>
											{:else}
												<span class="text-muted-foreground">—</span>
											{/if}
										{:else}
											<span class="text-muted-foreground">{feature.starter}</span>
										{/if}
									</div>
									<div class="text-center text-sm">
										{#if typeof feature.professional === 'boolean'}
											{#if feature.professional}
												<span class="text-green-500">✓</span>
											{:else}
												<span class="text-muted-foreground">—</span>
											{/if}
										{:else}
											<span class="text-muted-foreground">{feature.professional}</span>
										{/if}
									</div>
									<div class="text-center text-sm">
										{#if typeof feature.enterprise === 'boolean'}
											{#if feature.enterprise}
												<span class="text-green-500">✓</span>
											{:else}
												<span class="text-muted-foreground">—</span>
											{/if}
										{:else}
											<span class="text-muted-foreground">{feature.enterprise}</span>
										{/if}
									</div>
								</div>
							{/each}
						</div>
					</div>
				{/each}
			</div>
		</div>
	</section>

	<!-- Additional Services Section (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="text-center space-y-4">
			<div class="subsection-subitem-text py-0.1 flex h-fit w-fit items-center justify-center rounded-full border px-2 font-[400] tracking-tighter mx-auto"
				 style="color: #F59E0B; background-color: color-mix(in srgb, #F59E0B 20%, transparent);">
				Additional Services
			</div>
			<h2 class="section-headline-text">Professional services</h2>
			<p class="section-subheadline-text">Optional services to help you get the most out of Evoprof and ensure successful implementation.</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each additionalServices as service (service.name)}
				<Card.Root class="hover:shadow-lg transition-all duration-300">
					<Card.Content class="p-6">
						<div class="space-y-4">
							<div>
								<Card.Title class="text-lg font-semibold mb-2">
									{service.name}
								</Card.Title>
								<Card.Description class="text-muted-foreground">
									{service.description}
								</Card.Description>
							</div>
							<div class="text-xl font-bold text-primary">
								{service.price}
							</div>
							<ul class="space-y-2">
								{#each service.features as feature}
									<li class="flex items-center gap-2 text-sm">
										<span class="text-green-500">✓</span>
										{feature}
									</li>
								{/each}
							</ul>
							<Button variant="outline" class="w-full" href="/contact-sales">
								Learn More
							</Button>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- FAQ Section with Animated Background -->
	<section class="section-spacing">
		<SectionHeader
			sectionName="Pricing FAQ"
			headline="Common questions about pricing"
			subheadline="<p>Find answers to frequently asked questions about our pricing, billing, and plan features. Still have questions? Contact our sales team for personalized assistance.</p>"
			color="#EF4444"
		/>
	</section>

	<!-- FAQ Content (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<Accordion.Root type="multiple" class="w-full max-w-3xl" variant="noBorderGap">
			{#each pricingFAQs as faq (faq.question)}
				<Accordion.Item value={faq.question} class="bg-gray rounded-2xl border">
					<Accordion.Trigger class="cursor-pointer">{faq.question}</Accordion.Trigger>
					<Accordion.Content>
						<p>{faq.answer}</p>
					</Accordion.Content>
				</Accordion.Item>
			{/each}
		</Accordion.Root>
	</section>

	<!-- CTA Section with Animated Background -->
	<section class="section-spacing">
		<SectionHeader
			sectionName="Ready to Get Started?"
			headline="Start your free trial today"
			subheadline="<p>Join hundreds of educational institutions already using Evoprof to track student skills and improve outcomes. No credit card required for your 30-day trial.</p>"
			color="#10B981"
		/>
	</section>

	<!-- CTA Content (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="flex flex-col items-center gap-8 text-center max-w-4xl">
			<!-- CTA Highlights -->
			<div class="grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
				<div class="space-y-3">
					<div class="text-4xl">🆓</div>
					<h3 class="text-lg font-semibold">30-Day Free Trial</h3>
					<p class="text-sm text-muted-foreground">Full access to all features in your chosen plan. No credit card required to start.</p>
				</div>
				<div class="space-y-3">
					<div class="text-4xl">📞</div>
					<h3 class="text-lg font-semibold">Expert Onboarding</h3>
					<p class="text-sm text-muted-foreground">Our team will help you set up Evoprof and train your staff for maximum success.</p>
				</div>
				<div class="space-y-3">
					<div class="text-4xl">🔄</div>
					<h3 class="text-lg font-semibold">Flexible Plans</h3>
					<p class="text-sm text-muted-foreground">Change plans anytime as your institution grows. No long-term contracts required.</p>
				</div>
			</div>

			<!-- Action Buttons -->
			<div class="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
				<Button size="lg" href="/signup" class="text-lg px-8 py-3">
					Start Free Trial
				</Button>
				<Button variant="outline" size="lg" href="/contact-sales" class="text-lg px-8 py-3">
					Talk to Sales
				</Button>
			</div>

			<!-- Trust Indicators -->
			<div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full max-w-2xl text-center">
				<div class="space-y-1">
					<div class="text-2xl font-bold text-primary">156+</div>
					<div class="text-xs text-muted-foreground">Institutions Trust Us</div>
				</div>
				<div class="space-y-1">
					<div class="text-2xl font-bold text-primary">99.9%</div>
					<div class="text-xs text-muted-foreground">Uptime Guarantee</div>
				</div>
				<div class="space-y-1">
					<div class="text-2xl font-bold text-primary">24/7</div>
					<div class="text-xs text-muted-foreground">Support Available</div>
				</div>
				<div class="space-y-1">
					<div class="text-2xl font-bold text-primary">30</div>
					<div class="text-xs text-muted-foreground">Day Free Trial</div>
				</div>
			</div>
		</div>
	</section>
</div>

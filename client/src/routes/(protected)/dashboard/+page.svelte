<!--
  Dashboard Page - Protected Route Example
  
  This page demonstrates how to use the authentication system in a protected route.
  Shows user information and provides logout functionality.
  
  Dependencies: auth hooks, auth store
  Used by: Authenticated users after login
-->

<script lang="ts">
	import { Button } from "$lib/components/ui/button";
	import { Card, CardContent, CardHeader, CardTitle } from "$lib/components/ui/card";
	import { useAuth } from "$lib/hooks/auth";
	import { goto } from "$app/navigation";
	import { onMount } from "svelte";

	// Get authentication state and methods
	const auth = useAuth();

	// Redirect to login if not authenticated
	onMount(() => {
		if (!auth.isAuthenticated) {
			goto('/login');
		}
	});

	/**
	 * Handle user logout
	 */
	function handleLogout() {
		auth.logout();
		goto('/login');
	}
</script>

<div class="container mx-auto p-6">
	<div class="mb-6 flex items-center justify-between">
		<h1 class="text-3xl font-bold">Dashboard</h1>
		<Button variant="outline" onclick={handleLogout}>
			Logout
		</Button>
	</div>

	{#if auth.isAuthenticated && auth.user}
		<div class="grid gap-6 md:grid-cols-2">
			<!-- User Information Card -->
			<Card>
				<CardHeader>
					<CardTitle>User Information</CardTitle>
				</CardHeader>
				<CardContent class="space-y-4">
					<div>
						<span class="text-sm font-medium text-gray-600">Email</span>
						<p class="text-lg">{auth.user.email}</p>
					</div>
					<div>
						<span class="text-sm font-medium text-gray-600">Username</span>
						<p class="text-lg">{auth.user.username}</p>
					</div>
					<div>
						<span class="text-sm font-medium text-gray-600">User ID</span>
						<p class="text-sm font-mono text-gray-500">{auth.user.id}</p>
					</div>
					{#if auth.user.user_role}
						<div>
							<span class="text-sm font-medium text-gray-600">Role</span>
							<p class="text-lg">{auth.user.user_role.role_name}</p>
						</div>
					{/if}
				</CardContent>
			</Card>

			<!-- Authentication Status Card -->
			<Card>
				<CardHeader>
					<CardTitle>Authentication Status</CardTitle>
				</CardHeader>
				<CardContent class="space-y-4">
					<div class="flex items-center gap-2">
						<div class="h-3 w-3 rounded-full bg-green-500"></div>
						<span class="text-sm">Authenticated</span>
					</div>
					<div>
						<span class="text-sm font-medium text-gray-600">Account Status</span>
						<p class="text-lg">
							{auth.user.is_active ? 'Active' : 'Inactive'}
						</p>
					</div>
					<div>
						<span class="text-sm font-medium text-gray-600">Staff Access</span>
						<p class="text-lg">
							{auth.user.is_staff ? 'Yes' : 'No'}
						</p>
					</div>
				</CardContent>
			</Card>
		</div>

		<!-- Quick Actions -->
		<div class="mt-6">
			<Card>
				<CardHeader>
					<CardTitle>Quick Actions</CardTitle>
				</CardHeader>
				<CardContent>
					<div class="flex gap-4">
						<Button variant="outline">
							View Profile
						</Button>
						<Button variant="outline">
							Settings
						</Button>
						<Button variant="outline">
							Help
						</Button>
					</div>
				</CardContent>
			</Card>
		</div>
	{:else}
		<!-- Loading or unauthenticated state -->
		<div class="flex items-center justify-center py-12">
			<div class="text-center">
				<div class="h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 mx-auto mb-4"></div>
				<p class="text-gray-600">Loading...</p>
			</div>
		</div>
	{/if}
</div>

<script lang="ts">
	/**
	 * Root Layout Component
	 *
	 * Initializes global application state including authentication context.
	 * Sets up the auth store that will be available to all child components
	 * through Svelte's context system.
	 *
	 * Dependencies: auth store, mode-watcher
	 * Used by: All pages and components in the application
	 */

	import type { Snippet } from "svelte";
	import "../app.css";
	import { ModeWatcher } from "mode-watcher";
	import { setAuthContext } from "$lib/stores/auth.svelte";
	import { onMount } from "svelte";

	let { children }: { children: Snippet<[]> } = $props();

	// Initialize authentication context for the entire application
	const authStore = setAuthContext();

	// Check authentication status when the app loads
	onMount(async () => {
		await authStore.checkAuthStatus();
	});
</script>

<ModeWatcher />
<main class="page-text font-sans">
	
	{@render children()}
</main>

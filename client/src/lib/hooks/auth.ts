/**
 * Authentication Hooks and Guards
 * 
 * This file provides utilities for protecting routes and checking authentication
 * status. Includes hooks for redirecting unauthenticated users and checking
 * user permissions for specific actions.
 * 
 * Dependencies: auth store, navigation
 * Used by: Protected routes, components requiring authentication
 */

import { goto } from '$app/navigation';
import { getAuthContext } from '$lib/stores/auth.svelte';

/**
 * Authentication guard hook
 * Redirects to login page if user is not authenticated
 * 
 * @param redirectTo - Optional redirect path after login (defaults to current path)
 * @returns Authentication status
 */
export function useAuthGuard(redirectTo?: string): boolean {
	const authStore = getAuthContext();
	
	if (!authStore.isAuthenticated) {
		// Store intended destination for post-login redirect
		const currentPath = typeof window !== 'undefined' ? window.location.pathname : '/';
		const loginUrl = `/login${redirectTo || currentPath !== '/login' ? `?redirect=${encodeURIComponent(redirectTo || currentPath)}` : ''}`;
		
		goto(loginUrl);
		return false;
	}
	
	return true;
}

/**
 * Permission guard hook
 * Checks if current user has required permission
 * 
 * @param permission - Required permission name
 * @param redirectTo - Optional redirect path if permission denied
 * @returns Permission status
 */
export function usePermissionGuard(permission: string, redirectTo: string = '/unauthorized'): boolean {
	const authStore = getAuthContext();
	
	if (!authStore.isAuthenticated) {
		useAuthGuard();
		return false;
	}
	
	if (!authStore.hasPermission(permission)) {
		goto(redirectTo);
		return false;
	}
	
	return true;
}

/**
 * Role guard hook
 * Checks if current user has required role
 * 
 * @param role - Required role name
 * @param redirectTo - Optional redirect path if role denied
 * @returns Role status
 */
export function useRoleGuard(role: string, redirectTo: string = '/unauthorized'): boolean {
	const authStore = getAuthContext();
	
	if (!authStore.isAuthenticated) {
		useAuthGuard();
		return false;
	}
	
	if (!authStore.hasRole(role)) {
		goto(redirectTo);
		return false;
	}
	
	return true;
}

/**
 * Get current user information
 * Returns null if not authenticated
 * 
 * @returns Current user or null
 */
export function useCurrentUser() {
	const authStore = getAuthContext();
	return authStore.user;
}

/**
 * Get authentication status
 * 
 * @returns Authentication state
 */
export function useAuth() {
	const authStore = getAuthContext();
	return {
		isAuthenticated: authStore.isAuthenticated,
		isLoading: authStore.isLoading,
		user: authStore.user,
		error: authStore.error,
		login: authStore.login,
		logout: authStore.logout,
		clearError: authStore.clearError,
		hasPermission: authStore.hasPermission,
		hasRole: authStore.hasRole
	};
}

/**
 * Authentication API Service
 * 
 * This file handles all API communication with the Django backend for
 * authentication operations. Includes login, token refresh, and user
 * data retrieval with proper error handling and type safety.
 * 
 * Dependencies: types/auth.ts, utils/cookies.ts
 * Used by: Auth store, login components
 */

import type { 
	LoginCredentials, 
	LoginResponse, 
	TokenRefreshResponse, 
	User,
	ApiResponse,
	AuthError 
} from '$types/auth';
import { getRefreshToken, setAccessToken, setRefreshToken, clearAuthTokens } from '$lib/utils/cookies';

/**
 * Base API configuration
 */
const API_BASE_URL = 'https://refactored-memory.onrender.com';
const AUTH_ENDPOINTS = {
	login: '/auth/login/',
	refresh: '/auth/token/refresh/',
	user: '/auth/users/' // For fetching user profile
} as const;

/**
 * API Error class for structured error handling
 */
export class ApiError extends Error {
	constructor(
		message: string,
		public status: number,
		public data?: AuthError
	) {
		super(message);
		this.name = 'ApiError';
	}
}

/**
 * Make authenticated API request with automatic token refresh
 * 
 * @param url - API endpoint URL
 * @param options - Fetch options
 * @returns Promise with API response
 */
async function apiRequest<T>(url: string, options: RequestInit = {}): Promise<T> {
	const response = await fetch(`${API_BASE_URL}${url}`, {
		...options,
		headers: {
			'Content-Type': 'application/json',
			...options.headers
		}
	});

	if (!response.ok) {
		const errorData = await response.json().catch(() => ({}));
		throw new ApiError(
			`API request failed: ${response.status}`,
			response.status,
			errorData
		);
	}

	return response.json();
}

/**
 * Login user with email and password
 * Returns JWT tokens and user data on success
 * 
 * @param credentials - User login credentials
 * @returns Promise with login response
 */
export async function loginUser(credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> {
	try {
		const response = await apiRequest<LoginResponse>(AUTH_ENDPOINTS.login, {
			method: 'POST',
			body: JSON.stringify(credentials)
		});

		// Store tokens in secure cookies
		setAccessToken(response.access);
		setRefreshToken(response.refresh);

		return {
			success: true,
			data: response,
			message: 'Login successful'
		};
	} catch (error) {
		if (error instanceof ApiError) {
			return {
				success: false,
				error: error.data,
				message: error.message
			};
		}
		
		return {
			success: false,
			message: 'Network error occurred'
		};
	}
}

/**
 * Refresh JWT access token using refresh token
 * Automatically updates access token cookie on success
 * 
 * @returns Promise with refresh response
 */
export async function refreshAccessToken(): Promise<ApiResponse<TokenRefreshResponse>> {
	try {
		const refreshToken = getRefreshToken();
		
		if (!refreshToken) {
			throw new ApiError('No refresh token available', 401);
		}

		const response = await apiRequest<TokenRefreshResponse>(AUTH_ENDPOINTS.refresh, {
			method: 'POST',
			body: JSON.stringify({ refresh: refreshToken })
		});

		// Update access token cookie
		setAccessToken(response.access);

		return {
			success: true,
			data: response,
			message: 'Token refreshed successfully'
		};
	} catch (error) {
		// Clear tokens on refresh failure
		clearAuthTokens();
		
		if (error instanceof ApiError) {
			return {
				success: false,
				error: error.data,
				message: 'Session expired. Please login again.'
			};
		}
		
		return {
			success: false,
			message: 'Failed to refresh session'
		};
	}
}

/**
 * Logout user by clearing authentication tokens
 * Note: Django backend doesn't require server-side logout for JWT
 */
export function logoutUser(): void {
	clearAuthTokens();
}

/**
 * Get current user profile data
 * Requires valid authentication token
 * 
 * @param userId - User ID to fetch profile for
 * @returns Promise with user data
 */
export async function getCurrentUser(userId: string): Promise<ApiResponse<User>> {
	try {
		const response = await apiRequest<User>(`${AUTH_ENDPOINTS.user}${userId}/`);
		
		return {
			success: true,
			data: response,
			message: 'User data retrieved successfully'
		};
	} catch (error) {
		if (error instanceof ApiError) {
			return {
				success: false,
				error: error.data,
				message: error.message
			};
		}
		
		return {
			success: false,
			message: 'Failed to fetch user data'
		};
	}
}

/**
 * Decode JWT token payload (client-side only for user info)
 * Note: This doesn't verify the token signature
 * 
 * @param token - JWT token to decode
 * @returns Decoded token payload or null
 */
export function decodeJwtPayload(token: string): any | null {
	try {
		const base64Url = token.split('.')[1];
		const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
		const jsonPayload = decodeURIComponent(
			atob(base64)
				.split('')
				.map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
				.join('')
		);
		
		return JSON.parse(jsonPayload);
	} catch (error) {
		console.error('Failed to decode JWT token:', error);
		return null;
	}
}

/**
 * Check if JWT token is expired
 * 
 * @param token - JWT token to check
 * @returns True if token is expired
 */
export function isTokenExpired(token: string): boolean {
	const payload = decodeJwtPayload(token);
	if (!payload || !payload.exp) {
		return true;
	}
	
	// JWT exp is in seconds, Date.now() is in milliseconds
	return Date.now() >= payload.exp * 1000;
}

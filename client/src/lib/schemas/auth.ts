/**
 * Authentication Zod Schemas
 * 
 * This file contains all Zod validation schemas used for authentication forms.
 * These schemas ensure type safety and provide client-side validation for
 * login, registration, and password reset forms.
 * 
 * Dependencies: zod
 * Used by: Login page, auth store, Superforms integration
 */

import { z } from 'zod';

/**
 * Login form validation schema
 * Validates email and password fields with appropriate constraints
 */
export const loginSchema = z.object({
	email: z
		.string()
		.min(1, 'Email is required')
		.email('Please enter a valid email address')
		.max(254, 'Email is too long'),
	password: z
		.string()
		.min(1, 'Password is required')
		.min(8, 'Password must be at least 8 characters')
		.max(128, 'Password is too long')
});

/**
 * Password reset request schema
 * Validates email field for password reset requests
 */
export const passwordResetSchema = z.object({
	email: z
		.string()
		.min(1, 'Email is required')
		.email('Please enter a valid email address')
		.max(254, 'Email is too long')
});

/**
 * Password reset confirmation schema
 * Validates new password and confirmation fields
 */
export const passwordResetConfirmSchema = z.object({
	password: z
		.string()
		.min(8, 'Password must be at least 8 characters')
		.max(128, 'Password is too long'),
	confirmPassword: z
		.string()
		.min(1, 'Please confirm your password')
}).refine((data) => data.password === data.confirmPassword, {
	message: "Passwords don't match",
	path: ["confirmPassword"]
});

/**
 * Type exports for use in components
 */
export type LoginFormData = z.infer<typeof loginSchema>;
export type PasswordResetFormData = z.infer<typeof passwordResetSchema>;
export type PasswordResetConfirmFormData = z.infer<typeof passwordResetConfirmSchema>;

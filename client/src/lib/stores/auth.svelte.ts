/**
 * Authentication Store - Reactive Class with Svelte 5 Context
 *
 * This file implements a reactive authentication store using Svelte 5 runes
 * and context pattern. Manages user authentication state, JWT tokens, and
 * provides methods for login, logout, and token refresh operations.
 *
 * Dependencies: svelte context, auth service, cookie utils, auth types
 * Used by: Layout components, auth pages, protected routes
 */

import { getContext, setContext } from 'svelte';
import type { User, LoginCredentials, AuthState, ApiResponse } from '$types/auth';
import {
	loginUser,
	logoutUser as apiLogoutUser,
	refreshAccessToken,
	getCurrentUser,
	decodeJwtPayload,
	isTokenExpired
} from '$lib/services/auth';
import {
	getAccessToken,
	getRefreshToken,
	hasAuthTokens,
	clearAuthTokens
} from '$lib/utils/cookies';

/**
 * Authentication state interface for the reactive class
 */
interface AuthStore extends AuthState {
	// Authentication methods
	login: (credentials: LoginCredentials) => Promise<boolean>;
	logout: () => void;
	refreshToken: () => Promise<boolean>;
	checkAuthStatus: () => Promise<void>;

	// User permission helpers
	hasPermission: (permission: string) => boolean;
	hasRole: (role: string) => boolean;

	// Utility methods
	clearError: () => void;
	isTokenValid: () => boolean;
}

/**
 * Reactive Authentication Store Class
 * Implements all authentication logic with Svelte 5 runes
 */
class AuthStoreClass implements AuthStore {
	// Reactive state using Svelte 5 runes
	user = $state<User | null>(null);
	accessToken = $state<string | null>(null);
	refreshToken = $state<string | null>(null);
	isLoading = $state<boolean>(false);
	error = $state<string | null>(null);

	// Derived reactive state
	isAuthenticated = $derived<boolean>(
		!!(this.user && this.accessToken && this.refreshToken)
	);

	constructor() {
		// Initialize auth state from cookies on instantiation
		this.initializeFromCookies();
	}

	/**
	 * Initialize authentication state from stored cookies
	 * Called during store instantiation
	 */
	private initializeFromCookies(): void {
		if (typeof window === 'undefined') return; // SSR guard

		const accessToken = getAccessToken();
		const refreshToken = getRefreshToken();

		if (accessToken && refreshToken) {
			this.accessToken = accessToken;
			this.refreshToken = refreshToken;

			// Decode user info from access token
			const payload = decodeJwtPayload(accessToken);
			if (payload) {
				this.user = {
					id: payload.user_id,
					username: payload.username,
					email: payload.email,
					user_role: null, // Will be fetched separately if needed
					is_active: true,
					is_staff: false,
					created_at: '',
					updated_at: '',
					last_login: null
				};
			}
		}
	}

	/**
	 * Login user with email and password
	 * Updates authentication state on success
	 *
	 * @param credentials - User login credentials
	 * @returns Promise resolving to success status
	 */
	login = async (credentials: LoginCredentials): Promise<boolean> => {
		this.isLoading = true;
		this.error = null;

		try {
			const response = await loginUser(credentials);

			if (response.success && response.data) {
				// Update reactive state
				this.accessToken = response.data.access;
				this.refreshToken = response.data.refresh;

				// Decode user info from token
				const payload = decodeJwtPayload(response.data.access);
				if (payload) {
					this.user = {
						id: payload.user_id,
						username: payload.username,
						email: payload.email,
						user_role: null,
						is_active: true,
						is_staff: false,
						created_at: '',
						updated_at: '',
						last_login: null
					};
				}

				return true;
			} else {
				// Handle login failure
				this.error = response.message || 'Login failed';
				return false;
			}
		} catch (error) {
			this.error = 'Network error occurred during login';
			return false;
		} finally {
			this.isLoading = false;
		}
	};

	/**
	 * Logout user and clear authentication state
	 * Clears cookies and resets reactive state
	 */
	logout = (): void => {
		// Clear API tokens
		apiLogoutUser();

		// Reset reactive state
		this.user = null;
		this.accessToken = null;
		this.refreshToken = null;
		this.error = null;
	};

	/**
	 * Refresh JWT access token using refresh token
	 * Updates access token on success
	 *
	 * @returns Promise resolving to success status
	 */
	refreshToken = async (): Promise<boolean> => {
		try {
			const response = await refreshAccessToken();

			if (response.success && response.data) {
				this.accessToken = response.data.access;
				return true;
			} else {
				// Refresh failed, logout user
				this.logout();
				return false;
			}
		} catch (error) {
			this.logout();
			return false;
		}
	};

	/**
	 * Check current authentication status
	 * Validates tokens and refreshes if necessary
	 */
	checkAuthStatus = async (): Promise<void> => {
		if (!hasAuthTokens()) {
			this.logout();
			return;
		}

		const accessToken = getAccessToken();
		if (accessToken && isTokenExpired(accessToken)) {
			// Try to refresh token
			const refreshed = await this.refreshToken();
			if (!refreshed) {
				this.logout();
			}
		}
	};

	/**
	 * Check if user has specific permission
	 *
	 * @param permission - Permission name to check
	 * @returns True if user has permission
	 */
	hasPermission = (permission: string): boolean => {
		if (!this.user || !this.user.user_role) {
			return false;
		}

		return this.user.user_role.permissions.some(
			p => p.permission_name === permission
		);
	};

	/**
	 * Check if user has specific role
	 *
	 * @param role - Role name to check
	 * @returns True if user has role
	 */
	hasRole = (role: string): boolean => {
		if (!this.user || !this.user.user_role) {
			return false;
		}

		return this.user.user_role.role_name === role;
	};

	/**
	 * Clear current error state
	 */
	clearError = (): void => {
		this.error = null;
	};

	/**
	 * Check if current access token is valid
	 *
	 * @returns True if token exists and is not expired
	 */
	isTokenValid = (): boolean => {
		const token = getAccessToken();
		return !!(token && !isTokenExpired(token));
	};
}

/**
 * Context key for authentication store
 */
const AUTH_CONTEXT_KEY = 'auth_store';

/**
 * Set authentication context in component tree
 * Call this in your root layout component
 *
 * @param key - Optional context key (defaults to AUTH_CONTEXT_KEY)
 * @returns Authentication store instance
 */
export const setAuthContext = (key: string = AUTH_CONTEXT_KEY): AuthStore => {
	const authStore = new AuthStoreClass();
	return setContext(key, authStore);
};

/**
 * Get authentication context from component tree
 * Call this in components that need access to auth state
 *
 * @param key - Optional context key (defaults to AUTH_CONTEXT_KEY)
 * @returns Authentication store instance
 */
export const getAuthContext = (key: string = AUTH_CONTEXT_KEY): AuthStore => {
	return getContext<AuthStore>(key);
};

/**
 * Authentication-related TypeScript interfaces and types
 * 
 * This file defines all the types used throughout the authentication system,
 * including user data, login credentials, API responses, and authentication state.
 */

/**
 * User role information from the backend
 */
export interface UserRole {
	id: number;
	role_name: string;
	permissions: Permission[];
}

/**
 * Permission information from the backend
 */
export interface Permission {
	id: number;
	permission_name: string;
}

/**
 * Complete user information as returned by the backend
 */
export interface User {
	id: string;
	username: string;
	email: string;
	user_role: UserRole | null;
	is_active: boolean;
	is_staff: boolean;
	created_at: string;
	updated_at: string;
	last_login: string | null;
}

/**
 * Login credentials submitted by the user
 */
export interface LoginCredentials {
	email: string;
	password: string;
}

/**
 * JWT token response from the backend login endpoint
 * Note: Backend only returns tokens, user info is decoded from JWT
 */
export interface LoginResponse {
	access: string;
	refresh: string;
}

/**
 * Token refresh response from the backend
 */
export interface TokenRefreshResponse {
	access: string;
}

/**
 * Authentication error response from the backend
 */
export interface AuthError {
	detail?: string;
	non_field_errors?: string[];
	email?: string[];
	password?: string[];
}

/**
 * Authentication state managed by the auth store
 */
export interface AuthState {
	user: User | null;
	accessToken: string | null;
	refreshToken: string | null;
	isAuthenticated: boolean;
	isLoading: boolean;
	error: string | null;
}

/**
 * Login form validation errors
 */
export interface LoginFormErrors {
	email?: string[];
	password?: string[];
	root?: string[];
}

/**
 * API response wrapper for consistent error handling
 */
export interface ApiResponse<T = any> {
	success: boolean;
	data?: T;
	error?: AuthError;
	message?: string;
}

/**
 * Authentication context for protected routes
 */
export interface AuthContext {
	user: User | null;
	isAuthenticated: boolean;
	hasPermission: (permission: string) => boolean;
	hasRole: (role: string) => boolean;
}
